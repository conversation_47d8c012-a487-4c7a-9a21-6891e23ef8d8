import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, TensorDataset, DataLoader
from torchvision import datasets, transforms
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import time

device = 'cuda' if torch.cuda.is_available() else 'cpu'

# 定义一些变量
batch_size = 128
learning_rate = 0.005
epochs = 100
log_interval = 100

# 导入数据
dataset = pd.read_csv('/personal/catenary_data.csv', header=None)
label = pd.read_csv('/personal/catenary_data_label.csv', header=None)
x_train = torch.from_numpy(dataset.head(90000).values).float()  # 训练集9w
x_test = torch.from_numpy(dataset.tail(10000).values).float()  # 测试集1w
y_train = torch.from_numpy(label.head(90000).values).float()
y_test = torch.from_numpy(label.tail(10000).values).float()

# 定义一个数据装载的类，继承torch.utils.data.Dataset的功能
class GetLoader(torch.utils.data.Dataset):
    def __init__(self, data_root, data_label):
        self.data = data_root
        self.label = data_label
    
    def __getitem__(self, index):
        data = self.data[index]
        labels = self.label[index]
        return data, labels
    
    def __len__(self):
        return len(self.data)

# 定义一个数据分装函数
def split_loader(features, labels, batch_size=10, rate=None):
    data = GetLoader(features, labels)
    num_train = int(features.shape[0] * rate)
    num_test = features.shape[0] - num_train
    data_train, data_test = torch.utils.data.random_split(data, [num_train, num_test])
    train_loader = torch.utils.data.DataLoader(data_train, batch_size=batch_size, shuffle=True)
    valid_loader = torch.utils.data.DataLoader(data_test, batch_size=batch_size, shuffle=False)
    return (train_loader, valid_loader)

# 切分train_loader和valid_loader：
train_loader, valid_loader = split_loader(x_train, y_train, batch_size=200, rate=0.9)

# 定义mse损失函数
def mse_cal(data_loader, net):
    data = data_loader.dataset
    X = data[:][0]
    y = data[:][1]
    yhat = net(X)
    return F.mse_loss(yhat, y)

# 定义网络结构
# relu,sigmoid,tanh
class net_Lin(nn.Module):
    def __init__(self, act_fun=torch.relu, in_features=2, n_hidden1=4, n_hidden2=4, n_hidden3=4, out_features=1,
                 bias=True, BN_model=None, momentum=0.1):
        super(net_Lin, self).__init__()
        self.linear1 = nn.Linear(in_features, n_hidden1, bias=bias)
        self.normalize1 = nn.BatchNorm1d(n_hidden1, momentum=momentum)
        self.linear2 = nn.Linear(n_hidden1, n_hidden2, bias=bias)
        self.normalize2 = nn.BatchNorm1d(n_hidden2, momentum=momentum)
        self.linear3 = nn.Linear(n_hidden2, n_hidden3, bias=bias)
        self.normalize3 = nn.BatchNorm1d(n_hidden3, momentum=momentum)
        self.linear4 = nn.Linear(n_hidden3, out_features, bias=bias)
        self.BN_model = BN_model
        self.act_fun = act_fun
    
    def forward(self, x):
        if self.BN_model == None:
            z1 = self.linear1(x)
            p1 = self.act_fun(z1)
            z2 = self.linear2(p1)
            p2 = self.act_fun(z2)
            z3 = self.linear3(p2)
            p3 = self.act_fun(z3)
            out = self.linear4(p3)
        elif self.BN_model == 'pre':
            z1 = self.normalize1(self.linear1(x))
            p1 = self.act_fun(z1)
            z2 = self.normalize2(self.linear2(p1))
            p2 = self.act_fun(z2)
            z3 = self.normalize3(self.linear3(p2))
            p3 = self.act_fun(z3)
            out = self.linear4(p3)
        elif self.BN_model == 'post':
            z1 = self.linear1(x)
            p1 = self.act_fun(z1)
            z2 = self.linear2(self.normalize1(p1))
            p2 = self.act_fun(z2)
            z3 = self.linear3(self.normalize2(p2))
            p3 = self.act_fun(z3)
            out = self.linear4(self.normalize3(p3))
        return out

# 测试代码
a = []
for index, i in enumerate(range(7)):
    a.append(index)

# 把训练封装为一个函数，完成训练的同时返回trainset和validset损失值
def fit_rec(net, criterion, optimizer, train_data, valid_data, epochs=3, cla=False, eva=mse_cal):
    train_loss = []
    valid_loss = []
    for i, epoch in enumerate(range(epochs)):
        net.train()
        for X, y in train_data:
            if cla == True:
                y = y.flatten().long()  # 如果是分类问题，需要对y进行整数转化
            X = X.to(device)
            yhat = net.forward(X)
            loss = criterion(yhat, y)
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
        net.eval()
        train_loss.append(eva(train_data, net).detach())
        valid_loss.append(eva(valid_data, net).detach())
        if (i + 1) % 5 == 0:
            print('已经训练了{}个epoch，当前Loss：{}'.format(i + 1, train_loss[i]))
    return train_loss, valid_loss

# 获得网络输入输出维度
input_ = x_train.shape[1]
output_ = y_train.shape[1]

# 实例化一个网络
net = net_Lin(act_fun=F.leaky_relu, in_features=input_, BN_model='pre', n_hidden1=120, n_hidden2=120, n_hidden3=120).to(device)

# 确定优化器
optimizer = torch.optim.Adam(net.parameters(), lr=learning_rate)

# 开始训练（time warning：CPU大概需要4分钟左右，GPU大概需要1分钟）
t_start = time.time()  # 计时开始
train_loss, valid_loss = fit_rec(net=net, criterion=nn.MSELoss(), optimizer=optimizer,
                                  train_data=train_loader,
                                  valid_data=valid_loader,
                                  epochs=epochs,
                                  cla=False,
                                  eva=mse_cal
                                 )
t_end = time.time()  # 计时结束
print('running time:', t_end - t_start, 's')

# 可视化loss随着训练次数的变化
# plt.plot(train_loss, label='train_loss')
plt.plot(valid_loss, label='valid_loss')
plt.yscale("log")
plt.xlabel('epochs')
plt.ylabel('mse')
plt.legend(loc=1)

# 在测试集上进行测试
# 定义一个计算r2的函数
def r2_score(y_true, y_pred):
    mean_y_true = np.mean(y_true)
    SS_residual = np.sum((y_true - y_pred) ** 2)
    SS_total = np.sum((y_true - mean_y_true) ** 2)
    r2 = 1 - (SS_residual / SS_total)
    return r2

net.eval()
test_r2 = r2_score(y_test.detach().numpy(), net(x_test).detach().numpy())
R2 = 'R2 = ' + str(round(test_r2, 3))

# 可视化在测试集上的预测效果
net.eval()
plt.scatter(net(x_test).detach().numpy(), y_test.detach().numpy(), alpha=0.5)
plt.title('ANN_test')
plt.xlabel("predict")
plt.ylabel("true")
a = np.arange(925, 1175, 1)
plt.plot(a, a, 'r--')  # 红色虚线
plt.text(1125, 1100, R2, size=13, family="fantasy", color="r", style="italic", weight="light",
         bbox=dict(facecolor="white", alpha=0.2))

# 模型的保存，以备将来调用
torch.save(net.state_dict(), 'ANN_linjinglong.pt')

# 查看模型参数
torch.load('ANN_linjinglong.pt')

# 将保存的模型赋予一个空模型a，加载保存的参数
a = net_Lin(act_fun=F.leaky_relu, in_features=10, BN_model='pre', n_hidden1=120, out_features=1, n_hidden2=120,
            n_hidden3=120)
a.load_state_dict(torch.load('ANN_linjinglong.pt'))

# 测试,调用模型成功
a.eval()
test_r2 = r2_score(y_test.detach().numpy(), a(x_test).detach().numpy())
print(test_r2)
