# 导入PyMuPdf库
import pymupdf
doc1 = pymupdf.open("/bohr/PyMuPdf-9q7v/v1/ja7b02983_si_001.pdf") # 打开一个pdf文档，可以自己上传一个pdf，修改文件路径
doc1

# 提取某一页的内容，例如第7页
text = doc1[6].get_text()
text

# 当然你也可以写一个循环，提取某几页的内容，放到一起
result = "" # 先定义一个空字符串，用于存放提取的内容
for i in range(6, 10): # 提取第7页到第10页的内容，注意：python中的括号是"前包后不包"
    text = doc1[i].get_text()
    result += text + " " # 把每页所得的内容拼接，中间留个空格
result.strip() # 移除字符串收尾指定字符，默认为空格或换行符
result

result = result.replace('\xa0', '') # 取掉无实义字符串'\xa0'
result = result.replace('\n', '') # 取掉换行符'\n'
result

from chemdataextractor.doc import Paragraph # 导入Paragraph
paragraph = Paragraph(result)
paragraph.sentences # 将段落划分为句子

sentence4 = paragraph.sentences[3]
sentence4

sentence4.ner_tags # 化学命名实体识别

# 将CNER（化学命名实体识别）与POS（词性标注）的结果合并
def get_tagged_pos_chem_tokens(sentence):
    _tagged_pos_chem_tokens = sentence.pos_tagged_tokens.copy()
    for i, chem_tag in enumerate(sentence.ner_tags):
        if chem_tag is not None:
            _tagged_pos_chem_tokens[i] = (_tagged_pos_chem_tokens[i][0], chem_tag)
    return _tagged_pos_chem_tokens

tagged_pos_chem_tokens = get_tagged_pos_chem_tokens(sentence4)
tagged_pos_chem_tokens

# 把一个pdf整个转换成文本文件（例如txt文件）
doc2 = pymupdf.open("/bohr/PyMuPdf-9q7v/v1/ja7b02983_si_001.pdf") # 打开一个pdf文档
out = open("/personal/s41586-024-08173-7.txt", "wb") # 创建一个文本文件，用于存放结果
for page in doc2: # 遍历pdf文档中的每一页
    text = page.get_text().encode("utf8") # 提取文本 (用UTF-8编码)
    out.write(text) # 把每一页的内容写到文本文件中
    out.write(bytes((12,))) # 写入页面分隔符 (form feed 0x0C)
out.close()

# 我们看一下结果
with open('/bohr/PyMuPdf-9q7v/v1/ja7b02983_si_001.pdf', 'r') as f:
    content = f.readlines()
content

doc2 = pymupdf.open("/bohr/PyMuPdf-9q7v/v1/s41586-024-08173-7.pdf") # 打开一个pdf文档
for page_index in range(len(doc2)): # 遍历每一页
    page = doc2[page_index] # 获取页面
    image_list = page.get_images()
    # 打印每一页中有几幅图片
    if image_list:
        print(f"Found {len(image_list)} images on page {page_index}")
    else:
        print("No images found on page", page_index)
    for image_index, img in enumerate(image_list, start=1): # enumerate the image list
        xref = img[0] # get the XREF of the image
        pix = pymupdf.Pixmap(doc2, xref) # create a Pixmap
        if pix.n - pix.alpha > 3: # CMYK: convert to RGB first
            pix = pymupdf.Pixmap(pymupdf.csRGB, pix)
        pix.save("/personal/page_%s-image_%s.png" % (page_index, image_index)) # save the image as png
        pix = None

import matplotlib.pyplot as plt # plt 用于显示图片
import matplotlib.image as mpimg # mpimg 用于读取图片
img1 = mpimg.imread('/personal/page_10-image_1.png') # 读取图片
img2 = mpimg.imread('/personal/page_12-image_1.png')
#结果展示
plt.imshow(img1)
