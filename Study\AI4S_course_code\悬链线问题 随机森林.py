# [1] 导入必要的库
import pandas as pd
import numpy as np
n = 1000

# [2] 将我们刚才生成的悬链线数据导入,header=None表示首行即是数据，不然默认首行是列标签
dataset = pd.read_csv('/personal/catenary_data.csv', nrows=n, header=None)
dataset

# [3] 查看数据表基本信息，内存使用情况
dataset.info()

# [4] 导入势能值列表，它要作为标签使用
label = pd.read_csv('/personal/catenary_data_label.csv', nrows=n, header=None)
label

# [5] 查看标签基本信息，内存使用情况
label.info()

# [6] 变量名变更，同时将其转化为numpy数据类型
X = np.array(dataset)
y = np.array(label)

# [7] 注意观察y的结构
y

# [8] 查看y的形状
y.shape

# [9] 将y拉平
y = y.ravel()
y # 注意再观察y的结构

# [10] 再次查看y的形状
y.shape

# [11] 将数据分为训练集和测试集，这里我们采用二八开，并且设置一个随机态，以保证训练结果的复现
from sklearn.model_selection import train_test_split
X_train, X_test, y_train, y_test = train_test_split(X,
                                                    y,
                                                    test_size=0.2,
                                                    random_state=324)

# [12] 训练随机森林解决回归问题
from sklearn.ensemble import RandomForestRegressor
regressor = RandomForestRegressor(n_estimators=200, random_state=0)
regressor.fit(X_train, y_train)
y_pred = regressor.predict(X_test)

# [13] 评估回归性能
from sklearn import metrics
print('Mean Absolute Error:', metrics.mean_absolute_error(y_test, y_pred))
print('Mean Squared Error:', metrics.mean_squared_error(y_test, y_pred))
print('Root Mean Squared Error:', np.sqrt(metrics.mean_squared_error(y_test, y_pred)))
print('r_square:', metrics.r2_score(y_test, y_pred))

# [14] 可视化预测结果
import matplotlib.pyplot as plt
plt.scatter(y_test, y_pred, alpha=0.3)
plt.plot([min(y_pred), max(y_pred)], [min(y_pred), max(y_pred)], color='r')  # 画y=x直线
plt.xlabel("true value")
plt.ylabel("predict")
plt.show()
