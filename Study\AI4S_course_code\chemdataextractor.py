# [26]
from chemdataextractor.doc import Paragraph
# 导入Paragraph

# [27]
with open('/personal/procedure.txt', 'r', encoding='utf-8') as f:
    text = f.read()
text

# [5]
paragraph = Paragraph(text)
# 创建Paragraph对象

# [28]
paragraph.sentences
# 将段落划分为句子

# [29]
len(paragraph.sentences)
# 查看段落被划分为几个句子

# [30]
sentence2 = paragraph.sentences[1]
#查看第二句

# [7]
sentence2.tokens
# 进行分词

# [8]
sentence2.pos_tagged_tokens
# 进行词性标注，结果为(词，词性)的列表。

# [9]
sentence2.ner_tags
# 进行实体识别

# [10]
# 将CNER（化学命名实体识别）与POS（词性标注）的结果合并
def get_tagged_pos_chem_tokens(sentence):
    _tagged_pos_chem_tokens = sentence.pos_tagged_tokens.copy()
    for i, chem_tag in enumerate(sentence.ner_tags):
        if chem_tag is not None:
            _tagged_pos_chem_tokens[i] = (_tagged_pos_chem_tokens[i][0], chem_tag)
    return _tagged_pos_chem_tokens

tagged_pos_chem_tokens = get_tagged_pos_chem_tokens(sentence2)
tagged_pos_chem_tokens

# [11]
import re
# 导入正则表达式包
pattern = "^g$"
# 匹配'g'，并且'g'前面是字符串的开头，后面是字符串的结尾
print(re.match(pattern, 'graph'))
# 执行结果'None'表示无法匹配'graph'，因为它虽然以'g'开头，但不以'g'结尾

# [12]
re.match(pattern, 'g')
# 执行结果返回re.Match对象，表明匹配成功

# [13]
pattern = "^kg$|^mL$"
# 匹配'kg'或'mL'
re.match(pattern, 'kg')

# [14]
(re.match(pattern, 'mL'))

# [15]
regex = {
    "UNIT-TIME": "^period[s]?$|^min(utes|s|\.)?$|^hour[s]?|^month[s]?|^week(s|end)?$|^sec(ond)?s$|^d$|^(?-i:h)$|^day[s]?$|^hr[s]?$|^time$",
    "UNIT-MASS": "^([mkµu]?(g|gram[m]?[e]?)|(pico|nano|micro|milli|centi|deci|kilo|mega)gram[m]?[e]?)[s]?$",
    "UNIT-MOLAR": "^([mnµμu]?(?-i:M)|(?-i:N)|(pico|nano|micro|milli|[mnµμu])?molar)$",
    "UNIT-AMOUNT": "^([mnµμu]|pico|nano|micro|milli)?mol[e]?[s]?$",
    "UNIT-VOL": "^([mkµu]?l|[cd]?m\^?3|(µ|pico|nano|micro|milli|centi|deci|kilo|mega)?lit(re|er|\.)[s]?)$",
    "TIME": "^(fort|over)?night(s|falls|long)?$",
    "VB-ADD": "^(pre|re)?(add|mix|pour|dispers|introduc)(e|s|ed|ing)?$",
    "VB-CHARGE": "^charge(d)?$",
    "VB-STIR": "(pre|re)?(stir)(red|ring)?",
    "VB-YIELD": "^((afford|furnish|obtain|result|yield)(s|es|ed|ing)?|(isolat|leav|provid)(e|s|es|ed|ing)?|(giv|produc)(e|es|ed|ing)|form(ed|ing)|gave|get)$",
    "B-CM": "^mixture$|^suspension$|^solution$"
}

# [16]
def get_tagged_pos_chem_unit_operate_tokens(sentence):
    _tagged_pos_chem_tokens = get_tagged_pos_chem_tokens(sentence)
    _tagged_pos_chem_unit_operate_tokens = _tagged_pos_chem_tokens.copy()
    for i, (word, tag) in enumerate(_tagged_pos_chem_tokens):
        for regex_tag, pattern in regex.items():
            if re.match(pattern, word):
                _tagged_pos_chem_unit_operate_tokens[i] = (word, regex_tag)
                break
    return _tagged_pos_chem_unit_operate_tokens

tagged_pos_chem_unit_operate_tokens = get_tagged_pos_chem_unit_operate_tokens(sentence2)
tagged_pos_chem_unit_operate_tokens

# [17]
import nltk
# 导入Natural Language Toolkit (nltk)
grammar = "CM: {<B-CM><I-CM>*}\n"
"NN-UNIT: {<UNIT-TIME|UNIT-MASS|UNIT-MOLAR|UNIT-AMOUNT|UNIT-VOL>}\n"
"PROP: {<CD><NN-UNIT>}\n"
"PROP: {<TIME>}\n"
"PROPS: {<-LRB-><PROP>(<,><PROP>)*<-RRB->}\n"
"CHEM: {<CM><PROP|PROPS>?}\n"
# grammar = "CM: {<B-CM><I-CM>*}\n"
# "NN-UNIT: {<UNIT-TIME|UNIT-MASS|UNIT-MOLAR|UNIT-AMOUNT|UNIT-VOL>}\n"
# "PROP: {<CD><NN-UNIT>}\n"
# "PROP: {<TIME>}\n"
# "PROPS: {<-LRB-><PROP>(<,><PROP>)*<-RRB->}\n"
# "CHEM: {<CM><PROP|PROPS>?}\n"
# "OPERATE: {<VB-ADD|VB-CHARGE|VB-STIR><PROP|PROPS>?}\n"
# "NounPhrase: {<DT>?<JJ|RB>*(<NN|NNS|CHEM>)+}\n"
# "PrepPhrase: {<IN><NounPhrase>}\n"
# "VerbPhrase: {(<NounPhrase|PrepPhrase>?<VBD>?<RB>?<OPERATE>)|(<RB>?<OPERATE><NounPhrase|PrepPhrase>)}\n"
# "VerbPhrase: {<IN><RB>?<OPERATE>}\n"
# "VerbPhrase: {<VerbPhrase><PrepPhrase><IN><PROP>}"

def grammar_parse(sentence):
    _tagged_pos_chem_unit_operate_tokens = get_tagged_pos_chem_unit_operate_tokens(sentence)
    cp = nltk.RegexpParser(grammar)
    return cp.parse(_tagged_pos_chem_unit_operate_tokens)

grammar_tree2 = grammar_parse(sentence2)
grammar_tree2.draw()

# [18]
for sentence in paragraph.sentences:
    grammar_tree = grammar_parse(sentence)
    grammar_tree.draw()
    # 此代码块运行后，会弹出一个画着语法树的窗口，但是偶尔窗口不会自动弹出，请检查电脑的任务栏
    # 关掉弹出的窗口后，notebook才会继续运行

# [19]
from typing import Tuple
from nltk.tree import Tree

def get_descend_nodes_with_label(node, label):
    if node.label() == label:
        yield node
    for child_node in node:
        if isinstance(child_node, Tuple):
            if child_node[-1] == label:
                yield child_node
        if isinstance(child_node, Tree):
            if child_node.label() == label:
                yield child_node
            else:
                for res in get_descend_nodes_with_label(child_node, label):
                    yield res

# [20]
# 下面尝试获得句子中的所有CHEN node的descend_node，可以改一下label，比如改成 'PROP'，看看结果有什么变化。
for chem_node in get_descend_nodes_with_label(node=grammar_tree2, label='CHEM'):
    chem_node.draw()

# [21]
def get_mol_name(cm_node):
    name_fragments = []
    for child_node in cm_node:
        name_fragments.append(child_node[0])
    return ' '.join(name_fragments)

# [22]
# 尝试一下，对照下图，结果是否正确
for cm_node in get_descend_nodes_with_label(node=grammar_tree2, label='CM'):
    print("名字： ", get_mol_name(cm_node))
    cm_node.draw()

# [23]
def get_prop_value_unit_type(prop_node):
    child_nodes = list(prop_node)
    if len(child_nodes) == 1:
        prop_value = child_nodes[0][0]
        prop_unit = None
        prop_type = child_nodes[0][1]
    elif len(child_nodes) == 2:
        prop_value = child_nodes[0][0]
        prop_unit = child_nodes[1][0][0]
        prop_type = child_nodes[1][0][1].split('-')[-1]
    else:
        raise ValueError(f"prop node应该只有1个或2个child_nodes, 但却遇到了{len(child_nodes)}个")
    return prop_value, prop_unit, prop_type

# [24]
# 获得所有的属性
for prop_node in get_descend_nodes_with_label(node=grammar_tree2, label='PROP'):
    print(get_prop_value_unit_type(prop_node))

# [25]
import pandas as pd
result_df = pd.DataFrame(columns=['molecule', 'TIME value', 'TIME unit', 'MASS value', 'MASS unit', 'MOLAR value', 'MOLAR unit', 'AMOUNT value', 'AMOUNT unit', 'VOL value', 'VOL unit'])
for sentence in paragraph.sentences:
    grammar_tree = grammar_parse(sentence)
    for chem_node in get_descend_nodes_with_label(node=grammar_tree, label='CHEM'):
        cm_node = list(get_descend_nodes_with_label(node=chem_node, label='CM'))[0]
        mol_name = get_mol_name(cm_node)
        mol_data = {'molecule': mol_name}
        for prop_node in get_descend_nodes_with_label(node=chem_node, label='PROP'):
            prop_value, prop_unit, prop_type = get_prop_value_unit_type(prop_node)
            mol_data[prop_type + ' value'] = prop_value
            mol_data[prop_type + ' unit'] = prop_unit
        mol_data_dataframe = pd.DataFrame(mol_data, index=[0])
        result_df = pd.concat([result_df, mol_data_dataframe], ignore_index=True)
result_df

# [26]
for sentence in paragraph.sentences:
    # 遍历所有sentence
    if str(sentence).find('DCM') != -1:
        # sentence的属性不是字符串，不能用find方法，因此先将其转化为字符串。
        break
        # 如果找到了，就跳出循环
paragraph.sentences.index(sentence)
# 返回当前sentence的索引，也即含有'DCM'的sentence编号

# [27]
# 我们来看一下这句话，果然有DCM。
paragraph.sentences[2]

# [28]
# 将这一句的语法树画出来
grammar_tree = grammar_parse(paragraph.sentences[2])
grammar_tree.draw()

# [29]
# 对正则规则进行修改
regex = {
    "UNIT-TIME": "^period[s]?$|^min(utes|s|\.)?$|^hour[s]?|^month[s]?|^week(s|end)?$|^sec(ond)?s$|^d$|^(?-i:h)$|^day[s]?$|^hr[s]?$|^time$",
    "UNIT-MASS": "^([mkµu]?(g|gram[m]?[e]?)|(pico|nano|micro|milli|centi|deci|kilo|mega)gram[m]?[e]?)[s]?$",
    "UNIT-MOLAR": "^([mnµμu]?(?-i:M)|(?-i:N)|(pico|nano|micro|milli|[mnµμu])?molar)$",
    "UNIT-AMOUNT": "^([mnµμu]|pico|nano|micro|milli)?mol[e]?[s]?$",
    "UNIT-VOL": "^([mkµu]?l|[cd]?m\^?3|(µ|pico|nano|micro|milli|centi|deci|kilo|mega)?lit(re|er|\.)[s]?)$",
    "TIME": "^(fort|over)?night(s|falls|long)?$",
    "VB-ADD": "^(pre|re)?(add|mix|pour|dispers|introduc)(e|s|ed|ing)?$",
    "VB-CHARGE": "^charge(d)?$",
    "VB-STIR": "(pre|re)?(stir)(red|ring)?",
    "VB-YIELD": "^((afford|furnish|obtain|result|yield)(s|es|ed|ing)?|(isolat|leav|provid)(e|s|es|ed|ing)?|(giv|produc)(e|es|ed|ing)|form(ed|ing)|gave|get)$",
    "B-CM": "^mixture$|^suspension$|^solution$|^DCM$"
    # 我们在这里添加了DCM
}

# [30]
import pandas as pd
result_df = pd.DataFrame(columns=['molecule', 'TIME value', 'TIME unit', 'MASS value', 'MASS unit', 'MOLAR value', 'MOLAR unit', 'AMOUNT value', 'AMOUNT unit', 'VOL value', 'VOL unit'])
for sentence in paragraph.sentences:
    grammar_tree = grammar_parse(sentence)
    for chem_node in get_descend_nodes_with_label(node=grammar_tree, label='CHEM'):
        cm_node = list(get_descend_nodes_with_label(node=chem_node, label='CM'))[0]
        mol_name = get_mol_name(cm_node)
        mol_data = {'molecule': mol_name}
        for prop_node in get_descend_nodes_with_label(node=chem_node, label='PROP'):
            prop_value, prop_unit, prop_type = get_prop_value_unit_type(prop_node)
            mol_data[prop_type + ' value'] = prop_value
            mol_data[prop_type + ' unit'] = prop_unit
        mol_data_dataframe = pd.DataFrame(mol_data, index=[0])
        result_df = pd.concat([result_df, mol_data_dataframe], ignore_index=True)
result_df
