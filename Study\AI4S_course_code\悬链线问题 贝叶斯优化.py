# 合并后的完整代码

# 1. 安装和导入必要的库
import numpy as np
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import Matern
from skopt import Optimizer
from skopt.space import Real, Integer
from skopt.utils import use_named_args
from skopt import gp_minimize
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error
from sklearn.preprocessing import StandardScaler
from skopt import BayesSearchCV

# 2. 首先加载数据
X = np.loadtxt('/personal/catenary_data.csv', delimiter=',')
y = np.loadtxt('/personal/catenary_data_label.csv', delimiter=',').ravel()

# 3. 数据预处理（标准化）
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)
X_scaled = X_scaled.astype(np.float16)

# 4. 数据集划分
X_train, X_test, y_train, y_test = train_test_split(X_scaled, y, test_size=0.3, random_state=42)

# 5. 定义超参数搜索空间
param_space = {
    'n_estimators': Integer(10, 200),
    'max_depth': Integer(3, 15),
    'min_samples_split': Integer(2, 5),
    'min_samples_leaf': Integer(1, 5),
    'max_features': Real(0.5, 1.0, prior='uniform')
}

# 6. 设置贝叶斯优化
opt = BayesSearchCV(
    RandomForestRegressor(random_state=42),
    param_space,
    n_iter=30,
    cv=3,
    # n_jobs=-1,
    scoring='neg_mean_squared_error'
)

# 7. 执行贝叶斯优化
opt.fit(X_train, y_train)

# 8. 输出最优超参数
print("最优超参数: ", opt.best_params_)

# 9. 在测试集上评估
y_pred = opt.predict(X_test)
mse = mean_squared_error(y_test, y_pred)
print("测试集 MSE: ", mse)
