import hanlp  # 导入hanlp包

tokenizer_cn = hanlp.load('CTB6_CONVSEG')  # 加载CTB_CONVSEG预训练模型进行分词任务

tokenizer_cn("分词就是将连续的字序列按照一定的规范重新组合成词序列的过程。")

from hanlp.utils.lang.en.english_tokenizer import tokenize_english  # 导入英文分词功能
tokenizer_en = tokenize_english  # 实例化
tokenizer_en('Mr. Hank<PERSON> bought hankcs.com for 1.5 thousand dollars.')

recognizer_cn = hanlp.load(hanlp.pretrained.ner.MSRA_NER_BERT_BASE_ZH)  # 导入并实例化中文命名实体

text = list('2023年1月13-20日，北京大学校长、中国科学院院士龚旗煌一行访问瑞士、法国，出席国际研究型大学联盟校长年会、达沃斯世界经济论坛及全球大学领导者论坛，访问巴黎萨克雷大学、巴黎综合理工大学、法国泰雷兹集团，其间拜会了中国驻法国特命全权大使卢沙野。')

recognizer_cn(text)  # 这里注意它的输入是对句子进行字符分割的列表, 因此在上一条语句中应用了list()函数

recognizer_en = hanlp.load(hanlp.pretrained.ner.CONLL03_NER_BERT_BASE_CASED_EN)  # 导入并实例化英文命名实体
recognizer_en(["President", "Obama", "is", "speaking", "at", "the", "White", "House"])

tagger_cn = hanlp.load(hanlp.pretrained.pos.CTB5_POS_RNN_FASTTEXT_ZH)  # 导入并实例化中文词性标注
tagger_cn(['我', '的', '希望', '是', '希望', '和平'])

tagger_en = hanlp.load(hanlp.pretrained.pos.PTB_POS_RNN_FASTTEXT_EN)  # 导入并实例化英文词性标注
tagger_cn(['I', 'banked', '2', 'dollars', 'in', 'a', 'bank', '.'])
