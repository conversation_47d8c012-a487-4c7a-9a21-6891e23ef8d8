import jieba
# 导入jieba包

text = '分词就是将连续的字序列按照一定的规范重新组合成词序列的过程。'

jieba.cut(text, cut_all=False)
# cut_all默认为False，代表精确模式分词

jieba.lcut(text, cut_all=False)

jieba.lcut(text, cut_all=True)
# cut_all为True，代表全模式分词

jieba.cut_for_search(text)
# 在精确模式的基础上，对长词再次切分，提高召回率，适合用于搜索引擎分词

jieba.lcut_for_search(text)

import jieba.posseg as pseg
# 导入词性标注功能
pseg.lcut("我爱北京天安门")

import jieba
txt = open("/bohr/jieba-trot/v1/红楼梦.txt", "r", encoding="gbk", errors='ignore').read()
words = jieba.lcut(txt)
counts = {}
for word in words:
    if len(word) == 1:
        continue
    else:
        counts[word] = counts.get(word, 0) + 1
items = list(counts.items())
items.sort(key=lambda x: x[1], reverse=True)
for i in range(15):
    word, count = items[i]
    print("{0:<10}{1:>5}".format(word, count))
